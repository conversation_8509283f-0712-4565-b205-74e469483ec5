package com.haoxueren.sqlite;

import android.app.Activity;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.util.Log;

import com.haoxueren.pray.MyApplication;
import com.haoxueren.helper.LifecycleObserver;

import java.io.File;
import java.net.Socket;
import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;

/**
 * 同步权限管理器
 * 负责管理数据库同步的权限控制
 */
public class SyncPermissionManager {
    
    private static final String TAG = "SyncPermissionManager";
    private static final long DEFAULT_TIMEOUT_MILLIS = 30 * 1000; // 30秒超时
    private static final long CLEANUP_INTERVAL_MILLIS = 60 * 1000; // 1分钟清理一次过期请求

    private static volatile SyncPermissionManager instance;
    private final Map<String, SyncRequestInfo> pendingRequests;
    private final Map<String, Runnable> timeoutTasks; // 超时任务映射
    private final Handler mainHandler;
    private final Runnable cleanupTask;
    
    private SyncPermissionManager() {
        this.pendingRequests = new ConcurrentHashMap<>();
        this.timeoutTasks = new ConcurrentHashMap<>();
        this.mainHandler = new Handler(Looper.getMainLooper());

        // 定期清理过期请求的任务
        this.cleanupTask = new Runnable() {
            @Override
            public void run() {
                cleanupExpiredRequests();
                mainHandler.postDelayed(this, CLEANUP_INTERVAL_MILLIS);
            }
        };

        // 启动定期清理任务
        mainHandler.postDelayed(cleanupTask, CLEANUP_INTERVAL_MILLIS);
    }
    
    public static SyncPermissionManager getInstance() {
        if (instance == null) {
            synchronized (SyncPermissionManager.class) {
                if (instance == null) {
                    instance = new SyncPermissionManager();
                }
            }
        }
        return instance;
    }
    
    /**
     * 处理数据库同步请求
     * 这是主要的入口方法，替代原来的直接发送数据库文件
     */
    public void handleSyncRequest(Socket clientSocket, OutputStreamProxy outputProxy) {
        try {
            // 生成唯一的请求ID
            String requestId = UUID.randomUUID().toString();
            
            // 创建请求信息
            SyncRequestInfo requestInfo = new SyncRequestInfo(requestId, clientSocket, outputProxy);
            
            // 添加到待处理请求列表
            pendingRequests.put(requestId, requestInfo);
            
            Log.i(TAG, "收到同步请求: " + requestInfo.getRequestDescription());
            
            // 显示权限确认对话框
            showPermissionDialog(requestInfo);
            
            // 设置超时处理
            setupTimeout(requestInfo);
            
        } catch (Exception e) {
            Log.e(TAG, "处理同步请求时发生错误", e);
            try {
                outputProxy.write("error: " + e.getMessage());
            } catch (Exception ex) {
                Log.e(TAG, "发送错误响应失败", ex);
            }
            closeSocket(clientSocket);
        }
    }
    
    /**
     * 显示权限确认对话框
     */
    private void showPermissionDialog(SyncRequestInfo requestInfo) {
        mainHandler.post(() -> {
            try {
                Activity activity = getCurrentActivity();
                if (activity == null || activity.isFinishing() || activity.isDestroyed()) {
                    // 如果没有当前Activity或Activity不可用，发送通知
                    showPermissionNotification(requestInfo);
                    return;
                }

                SyncPermissionDialog dialog = new SyncPermissionDialog(activity, requestInfo);
                dialog.show(approved -> {
                    handleUserResponse(requestInfo.getRequestId(), approved);
                });

            } catch (Exception e) {
                Log.e(TAG, "显示权限对话框失败", e);
                handleUserResponse(requestInfo.getRequestId(), false);
            }
        });
    }
    
    /**
     * 显示权限确认通知（当应用在后台时）
     */
    private void showPermissionNotification(SyncRequestInfo requestInfo) {
        try {
            Log.i(TAG, "应用在后台，显示通知: " + requestInfo.getRequestDescription());

            Context context = MyApplication.getContext();
            SyncPermissionNotification notification = new SyncPermissionNotification(context);

            if (notification.hasNotificationPermission()) {
                notification.showPermissionNotification(requestInfo);
                Log.i(TAG, "权限确认通知已显示");
            } else {
                Log.w(TAG, "没有通知权限，自动拒绝请求");
                handleUserResponse(requestInfo.getRequestId(), false);
            }

        } catch (Exception e) {
            Log.e(TAG, "显示权限确认通知失败", e);
            handleUserResponse(requestInfo.getRequestId(), false);
        }
    }
    
    /**
     * 获取当前Activity
     */
    private Activity getCurrentActivity() {
        return LifecycleObserver.getInstance().getCurrentActivity();
    }
    
    /**
     * 设置超时处理
     */
    private void setupTimeout(SyncRequestInfo requestInfo) {
        String requestId = requestInfo.getRequestId();

        Runnable timeoutTask = () -> {
            if (pendingRequests.containsKey(requestId) &&
                requestInfo.getStatus() == SyncRequestInfo.RequestStatus.PENDING) {

                Log.i(TAG, "请求超时: " + requestInfo.getRequestDescription());
                requestInfo.timeout();
                handleUserResponse(requestId, false);
            }
        };

        // 保存超时任务引用，以便可以取消
        timeoutTasks.put(requestId, timeoutTask);
        mainHandler.postDelayed(timeoutTask, DEFAULT_TIMEOUT_MILLIS);
    }
    
    /**
     * 处理用户响应
     */
    public void handleUserResponse(String requestId, boolean approved) {
        SyncRequestInfo requestInfo = pendingRequests.remove(requestId);
        if (requestInfo == null) {
            Log.w(TAG, "未找到请求: " + requestId);
            return;
        }

        // 取消超时任务
        cancelTimeout(requestId);

        try {
            if (approved) {
                Log.i(TAG, "用户同意同步请求: " + requestInfo.getRequestDescription());
                sendDatabaseFile(requestInfo);
            } else {
                Log.i(TAG, "用户拒绝同步请求: " + requestInfo.getRequestDescription());
                sendDeniedResponse(requestInfo);
            }

            // 完成用户响应
            requestInfo.completeUserResponse(approved);

        } catch (Exception e) {
            Log.e(TAG, "处理用户响应时发生错误", e);
            requestInfo.error(e);

            // 发送错误响应
            try {
                requestInfo.getOutputProxy().write("error: " + e.getMessage());
            } catch (Exception ex) {
                Log.e(TAG, "发送错误响应失败", ex);
            }
        } finally {
            closeSocket(requestInfo.getClientSocket());
        }
    }

    /**
     * 取消超时任务
     */
    private void cancelTimeout(String requestId) {
        Runnable timeoutTask = timeoutTasks.remove(requestId);
        if (timeoutTask != null) {
            mainHandler.removeCallbacks(timeoutTask);
        }
    }
    
    /**
     * 发送数据库文件
     */
    private void sendDatabaseFile(SyncRequestInfo requestInfo) {
        try {
            Context context = MyApplication.getContext();
            File database = context.getDatabasePath("HaoPray.db");
            
            if (!database.exists()) {
                requestInfo.getOutputProxy().write("database not found");
                return;
            }
            
            Log.i(TAG, "开始发送数据库文件，大小: " + database.length() + " bytes");
            
            // 发送数据库文件
            requestInfo.getOutputProxy().write(database);
            
            Log.i(TAG, "数据库文件发送完成");
            
        } catch (Exception e) {
            Log.e(TAG, "发送数据库文件失败", e);
            try {
                requestInfo.getOutputProxy().write("error: " + e.getMessage());
            } catch (Exception ex) {
                Log.e(TAG, "发送错误响应失败", ex);
            }
        }
    }
    
    /**
     * 发送拒绝响应
     */
    private void sendDeniedResponse(SyncRequestInfo requestInfo) {
        try {
            requestInfo.getOutputProxy().write("sync request denied by user");
            Log.i(TAG, "已发送拒绝响应");
        } catch (Exception e) {
            Log.e(TAG, "发送拒绝响应失败", e);
        }
    }
    
    /**
     * 关闭Socket连接
     */
    private void closeSocket(Socket socket) {
        try {
            if (socket != null && !socket.isClosed()) {
                socket.shutdownOutput();
                socket.close();
            }
        } catch (Exception e) {
            Log.e(TAG, "关闭Socket失败", e);
        }
    }
    
    /**
     * 获取待处理请求数量
     */
    public int getPendingRequestCount() {
        return pendingRequests.size();
    }
    
    /**
     * 清理过期的请求
     */
    public void cleanupExpiredRequests() {
        long currentTime = System.currentTimeMillis();

        pendingRequests.entrySet().removeIf(entry -> {
            String requestId = entry.getKey();
            SyncRequestInfo requestInfo = entry.getValue();

            if (requestInfo.isExpired(DEFAULT_TIMEOUT_MILLIS * 2)) { // 给予额外的缓冲时间
                Log.i(TAG, "清理过期请求: " + requestInfo.getRequestDescription());

                // 取消超时任务
                cancelTimeout(requestId);

                // 发送超时响应
                try {
                    requestInfo.getOutputProxy().write("request timeout");
                } catch (Exception e) {
                    Log.e(TAG, "发送超时响应失败", e);
                }

                // 关闭连接
                closeSocket(requestInfo.getClientSocket());

                // 标记为超时
                requestInfo.timeout();

                return true;
            }
            return false;
        });

        Log.d(TAG, "当前待处理请求数量: " + pendingRequests.size());
    }

    /**
     * 强制清理所有待处理请求
     */
    public void forceCleanupAllRequests() {
        Log.i(TAG, "强制清理所有待处理请求，数量: " + pendingRequests.size());

        for (Map.Entry<String, SyncRequestInfo> entry : pendingRequests.entrySet()) {
            String requestId = entry.getKey();
            SyncRequestInfo requestInfo = entry.getValue();

            // 取消超时任务
            cancelTimeout(requestId);

            // 发送取消响应
            try {
                requestInfo.getOutputProxy().write("request cancelled");
            } catch (Exception e) {
                Log.e(TAG, "发送取消响应失败", e);
            }

            // 关闭连接
            closeSocket(requestInfo.getClientSocket());
        }

        pendingRequests.clear();
        timeoutTasks.clear();
    }
}
