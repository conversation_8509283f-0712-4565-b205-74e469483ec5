package com.haoxueren.sqlite;

import java.net.Socket;
import java.util.concurrent.CompletableFuture;

/**
 * 同步请求信息类
 * 包含同步请求的所有相关信息
 */
public class SyncRequestInfo {
    
    // 请求的唯一标识
    private String requestId;
    
    // 请求设备的IP地址
    private String deviceIp;
    
    // 请求设备的端口
    private int devicePort;
    
    // 请求设备的名称（如果可获取）
    private String deviceName;
    
    // 客户端Socket连接
    private Socket clientSocket;
    
    // 输出流代理
    private OutputStreamProxy outputProxy;
    
    // 请求时间戳
    private long requestTime;
    
    // 用户响应的Future
    private CompletableFuture<Boolean> userResponseFuture;
    
    // 请求状态
    public enum RequestStatus {
        PENDING,    // 等待用户响应
        APPROVED,   // 用户同意
        DENIED,     // 用户拒绝
        TIMEOUT,    // 超时
        ERROR       // 错误
    }
    
    private RequestStatus status;
    
    public SyncRequestInfo(String requestId, Socket clientSocket, OutputStreamProxy outputProxy) {
        this.requestId = requestId;
        this.clientSocket = clientSocket;
        this.outputProxy = outputProxy;
        this.requestTime = System.currentTimeMillis();
        this.status = RequestStatus.PENDING;
        this.userResponseFuture = new CompletableFuture<>();
        
        // 从Socket获取设备信息
        if (clientSocket != null && clientSocket.getRemoteSocketAddress() != null) {
            String remoteAddress = clientSocket.getRemoteSocketAddress().toString();
            // 解析IP地址，格式通常为 "/*************:12345"
            if (remoteAddress.startsWith("/")) {
                remoteAddress = remoteAddress.substring(1);
            }
            String[] parts = remoteAddress.split(":");
            if (parts.length >= 2) {
                this.deviceIp = parts[0];
                try {
                    this.devicePort = Integer.parseInt(parts[1]);
                } catch (NumberFormatException e) {
                    this.devicePort = 0;
                }
            }
        }
        
        // 设置默认设备名称
        this.deviceName = "未知设备 (" + deviceIp + ")";
    }
    
    // Getters and Setters
    public String getRequestId() {
        return requestId;
    }
    
    public String getDeviceIp() {
        return deviceIp;
    }
    
    public void setDeviceIp(String deviceIp) {
        this.deviceIp = deviceIp;
    }
    
    public int getDevicePort() {
        return devicePort;
    }
    
    public void setDevicePort(int devicePort) {
        this.devicePort = devicePort;
    }
    
    public String getDeviceName() {
        return deviceName;
    }
    
    public void setDeviceName(String deviceName) {
        this.deviceName = deviceName;
    }
    
    public Socket getClientSocket() {
        return clientSocket;
    }
    
    public OutputStreamProxy getOutputProxy() {
        return outputProxy;
    }
    
    public long getRequestTime() {
        return requestTime;
    }
    
    public RequestStatus getStatus() {
        return status;
    }
    
    public void setStatus(RequestStatus status) {
        this.status = status;
    }
    
    public CompletableFuture<Boolean> getUserResponseFuture() {
        return userResponseFuture;
    }
    
    /**
     * 完成用户响应
     */
    public void completeUserResponse(boolean approved) {
        this.status = approved ? RequestStatus.APPROVED : RequestStatus.DENIED;
        this.userResponseFuture.complete(approved);
    }
    
    /**
     * 设置超时
     */
    public void timeout() {
        this.status = RequestStatus.TIMEOUT;
        this.userResponseFuture.complete(false);
    }
    
    /**
     * 设置错误
     */
    public void error(Throwable throwable) {
        this.status = RequestStatus.ERROR;
        this.userResponseFuture.completeExceptionally(throwable);
    }
    
    /**
     * 获取请求描述信息
     */
    public String getRequestDescription() {
        return String.format("设备 %s (%s:%d) 请求同步数据库", 
            deviceName, deviceIp, devicePort);
    }
    
    /**
     * 检查请求是否已过期（超过指定时间）
     */
    public boolean isExpired(long timeoutMillis) {
        return System.currentTimeMillis() - requestTime > timeoutMillis;
    }
}
