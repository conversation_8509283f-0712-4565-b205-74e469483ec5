package com.haoxueren.sqlite;

import android.app.Notification;
import android.app.NotificationChannel;
import android.app.NotificationManager;
import android.app.PendingIntent;
import android.content.Context;
import android.content.Intent;
import android.os.Build;

import androidx.core.app.NotificationCompat;

import com.haoxueren.pray.R;
import com.haoxueren.pray.sync.DatabaseSyncActivity;

/**
 * 同步权限确认通知
 * 当应用在后台时，通过通知提醒用户有同步请求
 */
public class SyncPermissionNotification {
    
    private static final String CHANNEL_ID = "sync_permission_channel";
    private static final String CHANNEL_NAME = "数据库同步权限";
    private static final String CHANNEL_DESCRIPTION = "数据库同步权限确认通知";
    private static final int NOTIFICATION_ID = 1001;
    
    private Context context;
    private NotificationManager notificationManager;
    
    public SyncPermissionNotification(Context context) {
        this.context = context;
        this.notificationManager = (NotificationManager) context.getSystemService(Context.NOTIFICATION_SERVICE);
        createNotificationChannel();
    }
    
    /**
     * 创建通知渠道（Android 8.0+）
     */
    private void createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            NotificationChannel channel = new NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_HIGH
            );
            channel.setDescription(CHANNEL_DESCRIPTION);
            channel.enableLights(true);
            channel.enableVibration(true);
            channel.setShowBadge(true);
            
            notificationManager.createNotificationChannel(channel);
        }
    }
    
    /**
     * 显示同步权限确认通知
     */
    public void showPermissionNotification(SyncRequestInfo requestInfo) {
        // 创建点击通知时的Intent，打开数据库同步Activity
        Intent intent = new Intent(context, DatabaseSyncActivity.class);
        intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK | Intent.FLAG_ACTIVITY_CLEAR_TOP);
        intent.putExtra("sync_request_id", requestInfo.getRequestId());
        
        PendingIntent pendingIntent = PendingIntent.getActivity(
            context,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
        
        // 创建同意和拒绝的Action
        PendingIntent allowPendingIntent = createActionPendingIntent(
            requestInfo.getRequestId(), 
            "ALLOW_SYNC", 
            1
        );
        
        PendingIntent denyPendingIntent = createActionPendingIntent(
            requestInfo.getRequestId(), 
            "DENY_SYNC", 
            2
        );
        
        // 构建通知
        NotificationCompat.Builder builder = new NotificationCompat.Builder(context, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_sync_problem_48dp)
            .setContentTitle("数据库同步请求")
            .setContentText(requestInfo.getRequestDescription())
            .setStyle(new NotificationCompat.BigTextStyle()
                .bigText(String.format(
                    "%s\n\n⚠️ 警告：同意此请求将会把您的完整数据库发送给请求设备。\n\n请确认您信任该设备。",
                    requestInfo.getRequestDescription()
                )))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setCategory(NotificationCompat.CATEGORY_ALARM)
            .setAutoCancel(true)
            .setContentIntent(pendingIntent)
            .addAction(R.drawable.ic_ok, "同意", allowPendingIntent)
            .addAction(R.drawable.ic_delete_24x24, "拒绝", denyPendingIntent)
            .setDefaults(Notification.DEFAULT_ALL)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC);
        
        // 显示通知
        notificationManager.notify(NOTIFICATION_ID, builder.build());
    }
    
    /**
     * 创建Action的PendingIntent
     */
    private PendingIntent createActionPendingIntent(String requestId, String action, int requestCode) {
        Intent intent = new Intent(context, SyncPermissionReceiver.class);
        intent.setAction(action);
        intent.putExtra("request_id", requestId);
        
        return PendingIntent.getBroadcast(
            context,
            requestCode,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT | PendingIntent.FLAG_IMMUTABLE
        );
    }
    
    /**
     * 取消通知
     */
    public void cancelNotification() {
        notificationManager.cancel(NOTIFICATION_ID);
    }
    
    /**
     * 检查通知权限
     */
    public boolean hasNotificationPermission() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            return notificationManager.areNotificationsEnabled();
        }
        return true; // Android 13以下默认有权限
    }
}
