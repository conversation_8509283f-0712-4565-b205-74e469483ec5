# 数据库同步权限控制功能测试指南

## 🎯 测试目标

验证数据库同步权限控制功能的完整性和可靠性，确保用户数据安全。

## 📋 测试准备

### 环境要求
- 两台Android设备（或一台设备+模拟器）
- 设备连接到同一局域网
- HaoPray应用已安装并运行

### 测试数据准备
1. 在设备A上创建一些测试祷告记录
2. 确保设备B的数据库为空或有不同的数据
3. 记录两台设备的IP地址

## 🧪 测试用例

### 测试用例1：基本权限控制流程

**目标**：验证权限确认对话框正常显示和工作

**步骤**：
1. 设备A：打开HaoPray → 数据库同步
2. 设备B：打开HaoPray → 数据库同步
3. 设备A：扫描并选择设备B
4. 设备A：点击"同步数据库"
5. 设备B：观察是否弹出权限确认对话框
6. 设备B：检查对话框内容是否包含：
   - 请求设备信息（设备A的IP）
   - 安全警告文本
   - 倒计时显示
   - "同意"和"拒绝"按钮

**预期结果**：
- ✅ 设备B显示权限确认对话框
- ✅ 对话框信息准确显示设备A的信息
- ✅ 倒计时从30秒开始递减
- ✅ 按钮可正常点击

### 测试用例2：用户同意同步

**目标**：验证用户同意后数据库正常同步

**步骤**：
1. 重复测试用例1的步骤1-5
2. 设备B：点击"同意"按钮
3. 观察同步进度和结果

**预期结果**：
- ✅ 对话框立即关闭
- ✅ 设备A显示同步进度
- ✅ 数据库文件成功传输
- ✅ 设备A显示"同步成功"消息

### 测试用例3：用户拒绝同步

**目标**：验证用户拒绝后正确处理

**步骤**：
1. 重复测试用例1的步骤1-5
2. 设备B：点击"拒绝"按钮
3. 观察处理结果

**预期结果**：
- ✅ 对话框立即关闭
- ✅ 设备A显示"同步被拒绝"或类似错误消息
- ✅ 没有数据传输发生
- ✅ 设备B数据库保持不变

### 测试用例4：超时处理

**目标**：验证30秒超时机制

**步骤**：
1. 重复测试用例1的步骤1-5
2. 设备B：不点击任何按钮，等待30秒
3. 观察超时处理

**预期结果**：
- ✅ 倒计时到0时对话框自动关闭
- ✅ 设备A显示超时错误消息
- ✅ 没有数据传输发生

### 测试用例5：后台通知测试

**目标**：验证应用在后台时的通知功能

**步骤**：
1. 设备B：将HaoPray应用切换到后台
2. 设备A：发起同步请求
3. 设备B：观察通知栏
4. 设备B：点击通知中的"同意"或"拒绝"按钮

**预期结果**：
- ✅ 设备B显示权限确认通知
- ✅ 通知包含设备信息和操作按钮
- ✅ 点击按钮后正确处理用户选择
- ✅ 通知自动消失

### 测试用例6：多个并发请求

**目标**：验证多个设备同时请求的处理

**步骤**：
1. 准备3台设备（A、B、C）
2. 设备A和C同时向设备B发起同步请求
3. 观察设备B的处理情况

**预期结果**：
- ✅ 设备B能够处理多个请求
- ✅ 每个请求都有独立的确认流程
- ✅ 不会出现请求混乱或丢失

## 🔧 自动化测试

### 使用内置测试工具

1. **打开测试菜单**：
   - 数据库同步界面 → 菜单 → 测试功能 → 权限控制测试

2. **运行测试项目**：
   ```
   基本权限控制流程测试    - 测试完整流程
   超时处理测试           - 验证超时机制（需要35秒）
   用户同意响应测试       - 模拟用户同意
   用户拒绝响应测试       - 模拟用户拒绝
   清理过期请求测试       - 测试清理机制
   运行所有权限测试       - 执行完整测试套件
   ```

3. **查看测试日志**：
   ```bash
   # 使用adb查看日志
   adb logcat | grep -E "(SyncPermission|权限控制)"
   ```

## 📊 测试报告模板

### 测试环境
- 设备型号：
- Android版本：
- 应用版本：
- 网络环境：

### 测试结果

| 测试用例 | 状态 | 备注 |
|---------|------|------|
| 基本权限控制流程 | ✅/❌ | |
| 用户同意同步 | ✅/❌ | |
| 用户拒绝同步 | ✅/❌ | |
| 超时处理 | ✅/❌ | |
| 后台通知 | ✅/❌ | |
| 多个并发请求 | ✅/❌ | |

### 发现的问题
1. 
2. 
3. 

### 改进建议
1. 
2. 
3. 

## 🚨 常见问题排查

### 问题1：对话框不显示
**可能原因**：
- 应用没有前台Activity
- 权限管理器初始化失败

**排查方法**：
- 检查应用是否在前台
- 查看日志中的错误信息

### 问题2：通知不显示
**可能原因**：
- 通知权限未授予
- 通知渠道创建失败

**排查方法**：
- 检查应用通知权限
- 查看通知渠道设置

### 问题3：超时时间不准确
**可能原因**：
- 系统时间不同步
- Handler消息队列阻塞

**排查方法**：
- 检查系统时间
- 查看主线程是否阻塞

---

**注意**：测试过程中请保持网络稳定，避免因网络问题影响测试结果的准确性。
