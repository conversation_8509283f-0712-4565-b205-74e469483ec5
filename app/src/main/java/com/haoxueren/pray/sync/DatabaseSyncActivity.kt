package com.haoxueren.pray.sync

import android.annotation.SuppressLint
import android.content.ClipData
import android.content.ClipboardManager
import android.content.Context
import android.content.Intent
import android.os.Bundle
import android.view.View
import android.widget.*
import androidx.appcompat.app.AlertDialog
import androidx.appcompat.widget.Toolbar
import androidx.cardview.widget.CardView
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.google.android.material.floatingactionbutton.FloatingActionButton
import com.haoxueren.base.BaseActivity
import com.haoxueren.pray.R
import com.haoxueren.sqlite.DatabaseSyncManager
import com.haoxueren.sqlite.DeviceDiscoveryManager
import com.haoxueren.sqlite.EnhancedSocketManager
import com.haoxueren.sqlite.SyncErrorHandler
import com.haoxueren.sqlite.SyncTestHelper
import com.haoxueren.utils.ErrorUtils
import com.haoxueren.utils.ToastUtils
import com.haoxueren.utils.NetworkDiagnosticUtils
import com.haoxueren.utils.DeviceScanTestUtils
import com.haoxueren.utils.QuickNetworkTest
import com.haoxueren.pray.debug.NetworkDiagnosticActivity
import io.reactivex.disposables.CompositeDisposable

/**
 * 数据库同步界面
 * 提供设备发现、选择和数据库同步功能
 */
@SuppressLint("CheckResult")
class DatabaseSyncActivity : BaseActivity() {

    private lateinit var toolbar: Toolbar
    private lateinit var deviceRecyclerView: RecyclerView
    private lateinit var scanButton: Button
    private lateinit var addDeviceButton: Button
    private lateinit var syncButton: Button
    private lateinit var progressBar: ProgressBar
    private lateinit var statusTextView: TextView
    private lateinit var localInfoLayout: CardView
    private lateinit var localInfoTextView: TextView
    private lateinit var refreshFab: FloatingActionButton

    private lateinit var deviceAdapter: DeviceSyncAdapter
    private val devices = mutableListOf<DatabaseSyncManager.DeviceInfo>()
    private var selectedDevice: DatabaseSyncManager.DeviceInfo? = null
    private val compositeDisposable = CompositeDisposable()
    
    private var isScanning = false
    private var isSyncing = false

    companion object {
        fun start(context: Context) {
            val intent = Intent(context, DatabaseSyncActivity::class.java)
            context.startActivity(intent)
        }
    }

    override fun getLayoutResId(): Int {
        return R.layout.activity_database_sync
    }

    override fun bindView(layout: View) {
        toolbar = findViewById(R.id.toolbar)
        deviceRecyclerView = findViewById(R.id.deviceRecyclerView)
        scanButton = findViewById(R.id.scanButton)
        addDeviceButton = findViewById(R.id.addDeviceButton)
        syncButton = findViewById(R.id.syncButton)
        progressBar = findViewById(R.id.progressBar)
        statusTextView = findViewById(R.id.statusTextView)
        localInfoLayout = findViewById(R.id.localInfoLayout)
        localInfoTextView = findViewById(R.id.localInfoTextView)
        refreshFab = findViewById(R.id.refreshFab)
    }

    override fun initView() {
        // 设置Toolbar
        toolbar.setNavigationOnClickListener { finish() }

        // 添加测试菜单
        toolbar.inflateMenu(R.menu.menu_database_sync)
        toolbar.setOnMenuItemClickListener { menuItem ->
            when (menuItem.itemId) {
                R.id.action_test -> {
                    showTestMenu()
                    true
                }
                R.id.action_verify -> {
                    verifyDatabaseIntegrity()
                    true
                }
                R.id.action_diagnose -> {
                    showNetworkDiagnosis()
                    true
                }
                else -> false
            }
        }
        
        // 初始化设备列表
        setupDeviceRecyclerView()
        
        // 设置按钮点击事件
        setupButtons()
        
        // 显示本地设备信息
        showLocalDeviceInfo()

        // 设置本机信息点击事件
        setupLocalInfoClickListener()

        // 初始状态
        updateSyncButtonState()
        statusTextView.text = "请选择要同步的设备"
    }

    override fun initData() {
        // 启动Socket服务器
        EnhancedSocketManager.startEnhancedServer(2024)

        // 启动UDP响应服务器用于设备发现
        DeviceDiscoveryManager.getInstance().startUdpResponseServer()

        // 加载已发现的设备
        loadDiscoveredDevices()
    }

    override fun onDestroy() {
        super.onDestroy()
        compositeDisposable.clear()
        DeviceDiscoveryManager.getInstance().stopDeviceDiscovery()
    }

    private fun setupDeviceRecyclerView() {
        deviceAdapter = DeviceSyncAdapter(devices) { device ->
            onDeviceSelected(device)
        }
        
        deviceRecyclerView.layoutManager = LinearLayoutManager(this)
        deviceRecyclerView.adapter = deviceAdapter
    }

    private fun setupButtons() {
        scanButton.setOnClickListener {
            if (isScanning) {
                stopDeviceScanning()
            } else {
                startDeviceScanning()
            }
        }
        
        addDeviceButton.setOnClickListener {
            showAddDeviceDialog()
        }
        
        syncButton.setOnClickListener {
            selectedDevice?.let { device ->
                showSyncConfirmDialog(device)
            }
        }
        
        refreshFab.setOnClickListener {
            refreshDeviceList()
        }
    }

    private fun startDeviceScanning() {
        if (isScanning) return
        
        isScanning = true
        scanButton.text = "停止扫描"
        progressBar.visibility = View.VISIBLE
        statusTextView.text = "正在扫描局域网设备..."
        
        devices.clear()
        deviceAdapter.notifyDataSetChanged()
        
        DeviceDiscoveryManager.getInstance().startDeviceDiscovery(object : DeviceDiscoveryManager.DiscoveryCallback {
            override fun onDeviceFound(device: DatabaseSyncManager.DeviceInfo) {
                runOnUiThread {
                    devices.add(device)
                    deviceAdapter.notifyItemInserted(devices.size - 1)
                    statusTextView.text = "发现 ${devices.size} 台设备"
                }
            }

            override fun onScanProgress(message: String) {
                runOnUiThread {
                    statusTextView.text = message
                }
            }

            override fun onScanComplete(deviceList: List<DatabaseSyncManager.DeviceInfo>) {
                runOnUiThread {
                    isScanning = false
                    scanButton.text = "扫描设备"
                    progressBar.visibility = View.GONE

                    // 移除可能存在的本机设备
                    DeviceDiscoveryManager.getInstance().removeLocalDevice()

                    // 重新加载设备列表，确保不包含本机设备
                    val filteredDevices = DeviceDiscoveryManager.getInstance().getDiscoveredDevices()
                    devices.clear()
                    devices.addAll(filteredDevices)
                    deviceAdapter.notifyDataSetChanged()

                    if (filteredDevices.isEmpty()) {
                        statusTextView.text = "未发现可用设备"
                    } else {
                        statusTextView.text = "扫描完成，发现 ${filteredDevices.size} 台设备"
                    }
                }
            }

            override fun onError(error: String) {
                runOnUiThread {
                    isScanning = false
                    scanButton.text = "扫描设备"
                    progressBar.visibility = View.GONE
                    statusTextView.text = "扫描失败: $error"
                    ToastUtils.showToast(error)
                }
            }
        })
    }

    private fun stopDeviceScanning() {
        DeviceDiscoveryManager.getInstance().stopDeviceDiscovery()
        isScanning = false
        scanButton.text = "扫描设备"
        progressBar.visibility = View.GONE
        statusTextView.text = "扫描已停止"
    }

    private fun showAddDeviceDialog() {
        val dialogView = layoutInflater.inflate(R.layout.dialog_add_device, null)
        val nameEditText = dialogView.findViewById<EditText>(R.id.deviceNameEditText)
        val ipEditText = dialogView.findViewById<EditText>(R.id.deviceIpEditText)
        val portEditText = dialogView.findViewById<EditText>(R.id.devicePortEditText)
        
        portEditText.setText("2024") // 默认端口
        
        AlertDialog.Builder(this)
            .setTitle("添加设备")
            .setView(dialogView)
            .setPositiveButton("添加") { _, _ ->
                val name = nameEditText.text.toString().trim()
                val ip = ipEditText.text.toString().trim()
                val portStr = portEditText.text.toString().trim()
                
                if (name.isEmpty() || ip.isEmpty() || portStr.isEmpty()) {
                    ToastUtils.showToast("请填写完整信息")
                    return@setPositiveButton
                }
                
                try {
                    val port = portStr.toInt()
                    addManualDevice(name, ip, port)
                } catch (e: NumberFormatException) {
                    ToastUtils.showToast("端口号格式错误")
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun addManualDevice(name: String, ip: String, port: Int) {
        // 检查是否是本机IP
        val localIp = NetworkDiagnosticUtils.getLocalIpAddress()
        if (ip == localIp) {
            ToastUtils.showToast("不能添加本机设备")
            return
        }

        val device = DatabaseSyncManager.DeviceInfo(name, ip, port)

        // 检查是否已存在
        for (existing in devices) {
            if (existing.ip == ip && existing.port == port) {
                ToastUtils.showToast("设备已存在")
                return
            }
        }

        devices.add(device)
        deviceAdapter.notifyItemInserted(devices.size - 1)

        DeviceDiscoveryManager.getInstance().addManualDevice(name, ip, port)
        ToastUtils.showToast("设备添加成功")
    }

    private fun onDeviceSelected(device: DatabaseSyncManager.DeviceInfo) {
        selectedDevice = device
        deviceAdapter.setSelectedDevice(device)
        updateSyncButtonState()
        
        statusTextView.text = "已选择: ${device.name}"
        
        // 获取远程设备信息
        getRemoteDeviceInfo(device)
    }

    private fun getRemoteDeviceInfo(device: DatabaseSyncManager.DeviceInfo) {
        val disposable = EnhancedSocketManager.getRemoteDatabaseInfo(device)
            .subscribe({ info ->
                val size = info.get("formatted_size")?.asString ?: "未知"
                val prayRecords = info.get("pray_records")?.asInt ?: 0
                val groupRecords = info.get("group_records")?.asInt ?: 0
                val lastModified = info.get("formatted_date")?.asString ?: "未知"
                
                statusTextView.text = "设备: ${device.name}\n" +
                    "数据库大小: $size\n" +
                    "祷告记录: $prayRecords 条\n" +
                    "分组记录: $groupRecords 条\n" +
                    "最后修改: $lastModified"
                
            }, { error ->
                statusTextView.text = "无法获取设备 ${device.name} 的详细信息\n错误: ${error.message}"
            })
        
        compositeDisposable.add(disposable)
    }

    private fun showSyncConfirmDialog(device: DatabaseSyncManager.DeviceInfo) {
        AlertDialog.Builder(this)
            .setTitle("确认同步")
            .setMessage("确定要从设备 \"${device.name}\" 同步数据库吗？\n\n" +
                "⚠️ 警告：此操作将完全覆盖本地数据库！\n" +
                "系统会在同步前自动备份当前数据库。")
            .setPositiveButton("确认同步") { _, _ ->
                startDatabaseSync(device)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun startDatabaseSync(device: DatabaseSyncManager.DeviceInfo) {
        if (isSyncing) return
        
        isSyncing = true
        syncButton.isEnabled = false
        progressBar.visibility = View.VISIBLE
        progressBar.progress = 0
        
        val callback = object : EnhancedSocketManager.SyncProgressCallback {
            override fun onProgress(message: String, progress: Int) {
                runOnUiThread {
                    statusTextView.text = message
                    progressBar.progress = progress
                }
            }

            override fun onSuccess(message: String) {
                runOnUiThread {
                    isSyncing = false
                    syncButton.isEnabled = true
                    progressBar.visibility = View.GONE
                    statusTextView.text = message
                    ToastUtils.showToast("同步成功！")
                    
                    // 刷新本地设备信息
                    showLocalDeviceInfo()
                }
            }

            override fun onError(error: String, throwable: Throwable) {
                runOnUiThread {
                    isSyncing = false
                    syncButton.isEnabled = true
                    progressBar.visibility = View.GONE
                    statusTextView.text = error

                    // 使用增强的错误处理
                    SyncErrorHandler.logError("数据库同步", throwable)
                    SyncErrorHandler.showErrorDialog(
                        this@DatabaseSyncActivity,
                        throwable,
                        { retrySync(device) },  // 重试回调
                        { restoreBackup() }     // 恢复备份回调
                    )
                }
            }
        }
        
        val disposable = EnhancedSocketManager.syncDatabaseEnhanced(device, callback)
            .subscribe({ result ->
                // 成功处理已在callback中完成
            }, { error ->
                // 错误处理已在callback中完成
            })
        
        compositeDisposable.add(disposable)
    }

    private fun updateSyncButtonState() {
        syncButton.isEnabled = selectedDevice != null && !isSyncing
    }

    private fun loadDiscoveredDevices() {
        // 首先移除可能存在的本机设备
        DeviceDiscoveryManager.getInstance().removeLocalDevice()

        val discoveredDevices = DeviceDiscoveryManager.getInstance().getDiscoveredDevices()
        devices.clear()
        devices.addAll(discoveredDevices)
        deviceAdapter.notifyDataSetChanged()

        if (devices.isNotEmpty()) {
            statusTextView.text = "已加载 ${devices.size} 台设备"
        }
    }

    private fun refreshDeviceList() {
        // 检查已选择设备的连接状态
        selectedDevice?.let { device ->
            val disposable = DeviceDiscoveryManager.getInstance().checkDeviceConnection(device)
                .subscribe({ isOnline ->
                    device.isOnline = isOnline
                    device.lastSeen = System.currentTimeMillis()
                    deviceAdapter.notifyDataSetChanged()
                    
                    if (!isOnline) {
                        ToastUtils.showToast("设备 ${device.name} 已离线")
                    }
                }, { error ->
                    device.isOnline = false
                    deviceAdapter.notifyDataSetChanged()
                })
            
            compositeDisposable.add(disposable)
        }
    }

    private fun showLocalDeviceInfo() {
        val disposable = DatabaseSyncManager.getInstance().getDatabaseInfo()
            .subscribe({ dbInfo ->
                val localDevice = DeviceDiscoveryManager.getInstance().getLocalDeviceInfo()
                val deviceName = localDevice?.name ?: "本机"
                val localIp = NetworkDiagnosticUtils.getLocalIpAddress() ?: "未知"

                localInfoTextView.text = "本机设备: $deviceName\n" +
                    "本机IP: $localIp\n" +
                    "数据库大小: ${dbInfo.getFormattedSize()}\n" +
                    "祷告记录: ${dbInfo.prayRecordCount} 条\n" +
                    "分组记录: ${dbInfo.groupRecordCount} 条\n" +
                    "最后修改: ${dbInfo.getFormattedDate()}"

            }, { error ->
                localInfoTextView.text = "无法获取本地数据库信息"
                ErrorUtils.onError(error)
            })

        compositeDisposable.add(disposable)
    }

    private fun setupLocalInfoClickListener() {
        localInfoLayout.setOnClickListener {
            showDetailedLocalDeviceInfo()
        }
    }

    private fun showDetailedLocalDeviceInfo() {
        val detailedInfo = DeviceDiscoveryManager.getInstance().getDetailedLocalDeviceInfo()

        AlertDialog.Builder(this)
            .setTitle("本机设备详细信息")
            .setMessage(detailedInfo)
            .setPositiveButton("确定", null)
            .setNeutralButton("复制IP") { _, _ ->
                val localIp = NetworkDiagnosticUtils.getLocalIpAddress()
                if (localIp != null) {
                    val clipboard = getSystemService(Context.CLIPBOARD_SERVICE) as android.content.ClipboardManager
                    val clip = android.content.ClipData.newPlainText("本机IP", localIp)
                    clipboard.setPrimaryClip(clip)
                    ToastUtils.showToast("IP地址已复制: $localIp")
                } else {
                    ToastUtils.showToast("无法获取IP地址")
                }
            }
            .show()
    }

    /**
     * 重试同步操作
     */
    private fun retrySync(device: DatabaseSyncManager.DeviceInfo) {
        // 先检查设备连接状态
        SyncErrorHandler.NetworkChecker.checkDeviceConnection(device, object : SyncErrorHandler.NetworkChecker.ConnectionCallback {
            override fun onSuccess() {
                // 设备在线，重新开始同步
                startDatabaseSync(device)
            }

            override fun onFailure(error: Throwable) {
                // 设备仍然离线，显示错误信息
                SyncErrorHandler.showErrorToast(error)
            }
        })
    }

    /**
     * 恢复备份数据库
     */
    private fun restoreBackup() {
        progressBar.visibility = View.VISIBLE
        statusTextView.text = "正在恢复备份数据库..."

        val disposable = DatabaseSyncManager.getInstance().autoRestoreLatestBackup()
            .subscribe({ success ->
                progressBar.visibility = View.GONE
                if (success) {
                    statusTextView.text = "已恢复到同步前状态"
                    ToastUtils.showToast("数据库已恢复")
                    showLocalDeviceInfo() // 刷新本地信息
                } else {
                    statusTextView.text = "恢复备份失败"
                    ToastUtils.showToast("恢复备份失败")
                }
            }, { error ->
                progressBar.visibility = View.GONE
                statusTextView.text = "恢复备份失败: ${error.message}"
                SyncErrorHandler.showErrorToast(error)
                ErrorUtils.onError(error)
            })

        compositeDisposable.add(disposable)
    }

    /**
     * 显示测试菜单
     */
    private fun showTestMenu() {
        val options = arrayOf(
            "生成测试数据",
            "清理测试数据",
            "验证数据库完整性",
            "性能测试",
            "压力测试",
            "设备扫描测试",
            "快速网络测试",
            "测试发现设备A",
            "测试发现设备B",
            "权限控制测试"
        )

        AlertDialog.Builder(this)
            .setTitle("测试功能")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> generateTestData()
                    1 -> cleanTestData()
                    2 -> verifyDatabaseIntegrity()
                    3 -> performanceTest()
                    4 -> stressTest()
                    5 -> showDeviceScanTest()
                    6 -> quickNetworkTest()
                    7 -> testDiscoverDeviceA()
                    8 -> testDiscoverDeviceB()
                    9 -> showPermissionControlTest()
                }
            }
            .show()
    }

    /**
     * 生成测试数据
     */
    private fun generateTestData() {
        progressBar.visibility = View.VISIBLE

        val disposable = SyncTestHelper.generateTestData(1000, 100)
            .subscribe({ message ->
                statusTextView.text = message
            }, { error ->
                progressBar.visibility = View.GONE
                ToastUtils.showToast("生成测试数据失败: ${error.message}")
                ErrorUtils.onError(error)
            }, {
                progressBar.visibility = View.GONE
                ToastUtils.showToast("测试数据生成完成")
                showLocalDeviceInfo() // 刷新本地信息
            })

        compositeDisposable.add(disposable)
    }

    /**
     * 清理测试数据
     */
    private fun cleanTestData() {
        AlertDialog.Builder(this)
            .setTitle("确认清理")
            .setMessage("确定要清理所有测试数据吗？")
            .setPositiveButton("确定") { _, _ ->
                progressBar.visibility = View.VISIBLE

                val disposable = SyncTestHelper.cleanTestData()
                    .subscribe({ message ->
                        statusTextView.text = message
                    }, { error ->
                        progressBar.visibility = View.GONE
                        ToastUtils.showToast("清理测试数据失败: ${error.message}")
                        ErrorUtils.onError(error)
                    }, {
                        progressBar.visibility = View.GONE
                        ToastUtils.showToast("测试数据清理完成")
                        showLocalDeviceInfo() // 刷新本地信息
                    })

                compositeDisposable.add(disposable)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 验证数据库完整性
     */
    private fun verifyDatabaseIntegrity() {
        progressBar.visibility = View.VISIBLE
        statusTextView.text = "正在验证数据库完整性..."

        val disposable = SyncTestHelper.verifyDatabaseIntegrity()
            .subscribe({ result ->
                progressBar.visibility = View.GONE

                AlertDialog.Builder(this)
                    .setTitle("数据库完整性检查")
                    .setMessage(result.report)
                    .setPositiveButton("确定", null)
                    .show()

                statusTextView.text = if (result.isValid) "数据库完整性检查通过" else "数据库完整性检查失败"

            }, { error ->
                progressBar.visibility = View.GONE
                statusTextView.text = "数据库完整性检查失败"
                ToastUtils.showToast("检查失败: ${error.message}")
                ErrorUtils.onError(error)
            })

        compositeDisposable.add(disposable)
    }

    /**
     * 性能测试
     */
    private fun performanceTest() {
        selectedDevice?.let { device ->
            val performanceTest = SyncTestHelper.PerformanceTest()
            performanceTest.start()

            // 模拟性能测试
            progressBar.visibility = View.VISIBLE
            statusTextView.text = "正在进行性能测试..."

            // 这里可以调用实际的同步方法进行性能测试
            // 为了演示，我们使用模拟数据
            android.os.Handler(android.os.Looper.getMainLooper()).postDelayed({
                performanceTest.end(1024 * 100) // 假设传输了100KB数据
                progressBar.visibility = View.GONE

                AlertDialog.Builder(this)
                    .setTitle("性能测试结果")
                    .setMessage(performanceTest.report)
                    .setPositiveButton("确定", null)
                    .show()

                statusTextView.text = "性能测试完成"
            }, 3000)

        } ?: run {
            ToastUtils.showToast("请先选择一个设备")
        }
    }

    /**
     * 压力测试
     */
    private fun stressTest() {
        selectedDevice?.let { device ->
            AlertDialog.Builder(this)
                .setTitle("压力测试")
                .setMessage("将进行10次连续同步测试，确定继续吗？")
                .setPositiveButton("开始") { _, _ ->
                    progressBar.visibility = View.VISIBLE

                    val callback = object : EnhancedSocketManager.SyncProgressCallback {
                        override fun onProgress(message: String, progress: Int) {
                            statusTextView.text = message
                        }
                        override fun onSuccess(message: String) {}
                        override fun onError(error: String, throwable: Throwable) {}
                    }

                    val disposable = SyncTestHelper.stressTest(device, 10, callback)
                        .subscribe({ message ->
                            statusTextView.text = message
                        }, { error ->
                            progressBar.visibility = View.GONE
                            ToastUtils.showToast("压力测试失败: ${error.message}")
                            ErrorUtils.onError(error)
                        }, {
                            progressBar.visibility = View.GONE
                            ToastUtils.showToast("压力测试完成")
                        })

                    compositeDisposable.add(disposable)
                }
                .setNegativeButton("取消", null)
                .show()

        } ?: run {
            ToastUtils.showToast("请先选择一个设备")
        }
    }

    /**
     * 显示网络诊断信息
     */
    private fun showNetworkDiagnosis() {
        // 跳转到专门的网络诊断界面
        val intent = Intent(this, NetworkDiagnosticActivity::class.java)
        startActivity(intent)
    }

    /**
     * 显示连接测试对话框
     */
    private fun showConnectionTest() {
        val localIp = NetworkDiagnosticUtils.getLocalIpAddress()
        if (localIp == null) {
            ToastUtils.showToast("无法获取本机IP地址")
            return
        }

        val networkPrefix = localIp.substring(0, localIp.lastIndexOf('.') + 1)
        val testIps = arrayOf(
            "${networkPrefix}1",
            "${networkPrefix}2",
            "${networkPrefix}100",
            "${networkPrefix}101"
        )

        AlertDialog.Builder(this)
            .setTitle("选择测试IP")
            .setItems(testIps) { _, which ->
                testConnectionToIp(testIps[which])
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 测试连接到指定IP
     */
    private fun testConnectionToIp(ip: String) {
        progressBar.visibility = View.VISIBLE
        statusTextView.text = "正在测试连接到 $ip..."

        val disposable = NetworkDiagnosticUtils.testConnection(ip, 2024)
            .subscribe({ result ->
                progressBar.visibility = View.GONE
                statusTextView.text = result
                ToastUtils.showToast(result)
            }, { error ->
                progressBar.visibility = View.GONE
                statusTextView.text = "连接测试失败"
                ToastUtils.showToast("测试失败: ${error.message}")
            })

        compositeDisposable.add(disposable)
    }

    /**
     * 显示设备扫描测试菜单
     */
    private fun showDeviceScanTest() {
        val options = arrayOf(
            "完整扫描测试",
            "网络连通性测试",
            "UDP广播测试",
            "常见问题检查"
        )

        AlertDialog.Builder(this)
            .setTitle("设备扫描测试")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> {
                        ToastUtils.showToast("开始完整扫描测试，请查看日志")
                        DeviceScanTestUtils.performFullScanTest()
                    }
                    1 -> {
                        ToastUtils.showToast("开始网络连通性测试，请查看日志")
                        DeviceScanTestUtils.testNetworkConnectivity()
                    }
                    2 -> {
                        ToastUtils.showToast("开始UDP广播测试，请查看日志")
                        DeviceScanTestUtils.testUdpBroadcast()
                    }
                    3 -> {
                        ToastUtils.showToast("开始检查常见问题，请查看日志")
                        DeviceScanTestUtils.checkCommonIssues()
                    }
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 快速网络测试
     */
    private fun quickNetworkTest() {
        ToastUtils.showToast("开始快速网络测试，请查看日志")
        QuickNetworkTest.runFullNetworkTest()
    }

    /**
     * 测试发现设备A
     */
    private fun testDiscoverDeviceA() {
        ToastUtils.showToast("测试发现设备A (192.168.1.6)，请查看日志")
        QuickNetworkTest.testDiscoverDeviceA()
    }

    /**
     * 测试发现设备B
     */
    private fun testDiscoverDeviceB() {
        ToastUtils.showToast("测试发现设备B (192.168.1.10)，请查看日志")
        QuickNetworkTest.testDiscoverDeviceB()
    }

    /**
     * 显示权限控制测试菜单
     */
    private fun showPermissionControlTest() {
        val options = arrayOf(
            "基本权限控制流程测试",
            "超时处理测试",
            "用户同意响应测试",
            "用户拒绝响应测试",
            "清理过期请求测试",
            "运行所有权限测试"
        )

        AlertDialog.Builder(this)
            .setTitle("权限控制测试")
            .setItems(options) { _, which ->
                when (which) {
                    0 -> testBasicPermissionFlow()
                    1 -> testTimeoutHandling()
                    2 -> testUserApproval()
                    3 -> testUserDenial()
                    4 -> testCleanupExpiredRequests()
                    5 -> runAllPermissionTests()
                }
            }
            .setNegativeButton("取消", null)
            .show()
    }

    /**
     * 测试基本权限控制流程
     */
    private fun testBasicPermissionFlow() {
        ToastUtils.showToast("开始测试基本权限控制流程，请查看日志")
        Thread {
            com.haoxueren.sqlite.SyncPermissionTestUtils.testBasicPermissionFlow()
        }.start()
    }

    /**
     * 测试超时处理
     */
    private fun testTimeoutHandling() {
        ToastUtils.showToast("开始测试超时处理（需要等待35秒），请查看日志")
        Thread {
            com.haoxueren.sqlite.SyncPermissionTestUtils.testTimeoutHandling()
        }.start()
    }

    /**
     * 测试用户同意响应
     */
    private fun testUserApproval() {
        ToastUtils.showToast("开始测试用户同意响应，请查看日志")
        Thread {
            com.haoxueren.sqlite.SyncPermissionTestUtils.testUserApproval()
        }.start()
    }

    /**
     * 测试用户拒绝响应
     */
    private fun testUserDenial() {
        ToastUtils.showToast("开始测试用户拒绝响应，请查看日志")
        Thread {
            com.haoxueren.sqlite.SyncPermissionTestUtils.testUserDenial()
        }.start()
    }

    /**
     * 测试清理过期请求
     */
    private fun testCleanupExpiredRequests() {
        ToastUtils.showToast("开始测试清理过期请求，请查看日志")
        Thread {
            com.haoxueren.sqlite.SyncPermissionTestUtils.testCleanupExpiredRequests()
        }.start()
    }

    /**
     * 运行所有权限测试
     */
    private fun runAllPermissionTests() {
        ToastUtils.showToast("开始运行所有权限控制测试，请查看日志")
        Thread {
            com.haoxueren.sqlite.SyncPermissionTestUtils.runAllTests()
        }.start()
    }
}
