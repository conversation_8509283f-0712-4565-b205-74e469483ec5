package com.haoxueren.sqlite;

import android.content.Context;

import com.google.gson.Gson;
import com.google.gson.JsonObject;
import com.haoxueren.pray.MyApplication;
import com.haoxueren.proxy.InputStreamProxy;
import com.haoxueren.proxy.OutputStreamProxy;
import com.haoxueren.restful.GsonUtils;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.net.ServerSocket;
import java.net.Socket;
import java.net.SocketTimeoutException;

import io.reactivex.Observable;
import io.reactivex.android.schedulers.AndroidSchedulers;
import io.reactivex.schedulers.Schedulers;

/**
 * 增强的Socket管理器
 * 提供更完善的数据库同步功能，包括进度回调、错误处理、数据验证等
 */
public class EnhancedSocketManager {

    private static boolean serverRunning = false;
    private static ServerSocket serverSocket;
    private static final int DEFAULT_PORT = 2024;
    
    /**
     * 同步进度回调接口
     */
    public interface SyncProgressCallback {
        void onProgress(String message, int progress);
        void onSuccess(String message);
        void onError(String error, Throwable throwable);
    }
    
    /**
     * 启动增强的Socket服务器
     */
    public static void startEnhancedServer(int port) {
        if (serverRunning) {
            return;
        }
        
        new Thread(() -> {
            try {
                serverSocket = new ServerSocket(port);
                serverRunning = true;
                
                while (serverRunning) {
                    try {
                        Socket clientSocket = serverSocket.accept();
                        // 为每个客户端连接创建新线程处理
                        new Thread(() -> handleClientConnection(clientSocket)).start();
                        
                    } catch (IOException e) {
                        if (serverRunning) {
                            e.printStackTrace();
                        }
                    }
                }
                
            } catch (IOException e) {
                e.printStackTrace();
                serverRunning = false;
            }
        }).start();
    }
    
    /**
     * 处理客户端连接
     */
    private static void handleClientConnection(Socket clientSocket) {
        try {
            InputStream inputStream = clientSocket.getInputStream();
            OutputStream outputStream = clientSocket.getOutputStream();
            
            InputStreamProxy inputProxy = new InputStreamProxy(inputStream);
            OutputStreamProxy outputProxy = new OutputStreamProxy(outputStream);
            
            // 读取请求
            String requestStr = inputProxy.readString();
            clientSocket.shutdownInput();
            
            Gson gson = GsonUtils.getGson();
            JsonObject request = gson.fromJson(requestStr, JsonObject.class);
            String action = request.get("action").getAsString();
            
            switch (action) {
                case "action_sync_database":
                    handleDatabaseSyncRequest(outputProxy, clientSocket);
                    break;

                case "device_info":
                    handleDeviceInfoRequest(outputProxy, clientSocket);
                    break;

                case "ping":
                    handlePingRequest(outputProxy, clientSocket);
                    break;

                case "database_info":
                    handleDatabaseInfoRequest(outputProxy, clientSocket);
                    break;

                default:
                    outputProxy.write("unknown action: " + action);
                    closeSocket(clientSocket);
                    break;
            }
            
        } catch (Exception e) {
            e.printStackTrace();
            closeSocket(clientSocket);
        }
        // 注意：不在这里关闭Socket，由各个处理方法负责关闭
    }

    /**
     * 关闭Socket连接
     */
    private static void closeSocket(Socket socket) {
        try {
            if (socket != null && !socket.isClosed()) {
                socket.shutdownOutput();
                socket.close();
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 处理数据库同步请求
     * 现在使用权限控制机制，需要用户确认后才能发送数据库文件
     */
    private static void handleDatabaseSyncRequest(OutputStreamProxy outputProxy, Socket clientSocket) {
        try {
            Context context = MyApplication.getContext();
            File database = context.getDatabasePath("HaoPray.db");

            if (!database.exists()) {
                outputProxy.write("database not found");
                closeSocket(clientSocket);
                return;
            }

            // 使用权限管理器处理同步请求
            // 注意：这里不再直接关闭Socket，由权限管理器负责处理
            SyncPermissionManager.getInstance().handleSyncRequest(clientSocket, outputProxy);

        } catch (Exception e) {
            try {
                outputProxy.write("error: " + e.getMessage());
            } catch (Exception ex) {
                ex.printStackTrace();
            }
            closeSocket(clientSocket);
        }
        // 注意：不在这里关闭Socket，由权限管理器负责关闭
    }
    
    /**
     * 处理设备信息请求
     */
    private static void handleDeviceInfoRequest(OutputStreamProxy outputProxy, Socket clientSocket) {
        try {
            JsonObject response = new JsonObject();
            response.addProperty("device_name", android.os.Build.MODEL);
            response.addProperty("app_version", "HaoPray");
            response.addProperty("android_version", android.os.Build.VERSION.RELEASE);
            
            // 获取数据库信息
            DatabaseSyncManager.getInstance().getDatabaseInfo()
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io()) // 确保回调也在IO线程执行
                .subscribe(dbInfo -> {
                    try {
                        response.addProperty("database_size", dbInfo.fileSize);
                        response.addProperty("pray_records", dbInfo.prayRecordCount);
                        response.addProperty("group_records", dbInfo.groupRecordCount);
                        response.addProperty("last_modified", dbInfo.lastModified);

                        outputProxy.write(response.toString());
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        closeSocket(clientSocket);
                    }
                }, error -> {
                    try {
                        outputProxy.write(response.toString());
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        closeSocket(clientSocket);
                    }
                });
            
        } catch (Exception e) {
            try {
                outputProxy.write("error: " + e.getMessage());
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                closeSocket(clientSocket);
            }
        }
    }
    
    /**
     * 处理Ping请求
     */
    private static void handlePingRequest(OutputStreamProxy outputProxy, Socket clientSocket) {
        try {
            JsonObject response = new JsonObject();
            response.addProperty("status", "ok");
            response.addProperty("timestamp", System.currentTimeMillis());
            outputProxy.write(response.toString());
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeSocket(clientSocket);
        }
    }
    
    /**
     * 处理数据库信息请求
     */
    private static void handleDatabaseInfoRequest(OutputStreamProxy outputProxy, Socket clientSocket) {
        try {
            DatabaseSyncManager.getInstance().getDatabaseInfo()
                .subscribeOn(Schedulers.io())
                .observeOn(Schedulers.io()) // 确保回调也在IO线程执行
                .subscribe(dbInfo -> {
                    try {
                        JsonObject response = new JsonObject();
                        response.addProperty("file_size", dbInfo.fileSize);
                        response.addProperty("pray_records", dbInfo.prayRecordCount);
                        response.addProperty("group_records", dbInfo.groupRecordCount);
                        response.addProperty("last_modified", dbInfo.lastModified);
                        response.addProperty("formatted_size", dbInfo.getFormattedSize());
                        response.addProperty("formatted_date", dbInfo.getFormattedDate());

                        outputProxy.write(response.toString());
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        closeSocket(clientSocket);
                    }
                }, error -> {
                    try {
                        outputProxy.write("error: " + error.getMessage());
                    } catch (Exception e) {
                        e.printStackTrace();
                    } finally {
                        closeSocket(clientSocket);
                    }
                });
                
        } catch (Exception e) {
            try {
                outputProxy.write("error: " + e.getMessage());
            } catch (Exception ex) {
                ex.printStackTrace();
            } finally {
                closeSocket(clientSocket);
            }
        }
    }
    
    /**
     * 停止服务器
     */
    public static void stopServer() {
        serverRunning = false;
        if (serverSocket != null) {
            try {
                serverSocket.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
    }
    
    /**
     * 增强的数据库同步方法
     */
    public static Observable<String> syncDatabaseEnhanced(
            DatabaseSyncManager.DeviceInfo device,
            SyncProgressCallback callback) {

        final File[] backupFileRef = new File[1]; // 用于在回调中保存备份文件引用

        return Observable.<String>create(emitter -> {
            try {
                // 1. 创建备份
                callback.onProgress("正在备份当前数据库...", 10);
                DatabaseSyncManager.getInstance().createBackup()
                    .subscribeOn(Schedulers.io())
                    .observeOn(Schedulers.io()) // 确保后续操作也在IO线程
                    .subscribe(backup -> {
                        backupFileRef[0] = backup; // 保存备份文件引用
                        try {
                            // 2. 连接到远程设备
                            callback.onProgress("正在连接到 " + device.name + "...", 20);

                            Socket socket = new Socket();
                            socket.setSoTimeout(30000); // 30秒超时
                            socket.connect(new java.net.InetSocketAddress(device.ip, device.port), 30000);

                            // 3. 发送同步请求
                            OutputStream outputStream = socket.getOutputStream();
                            OutputStreamProxy outputProxy = new OutputStreamProxy(outputStream);

                            JsonObject request = new JsonObject();
                            request.addProperty("action", "action_sync_database");
                            outputProxy.write(request.toString());

                            callback.onProgress("正在请求数据库文件...", 30);
                            socket.shutdownOutput();

                            // 4. 接收数据库文件
                            InputStream inputStream = socket.getInputStream();
                            InputStreamProxy inputProxy = new InputStreamProxy(inputStream);

                            Context context = MyApplication.getContext();
                            File tempDb = context.getDatabasePath("HaoPray_temp.db");

                            callback.onProgress("正在下载数据库文件...", 40);

                            inputProxy.readFile(tempDb, progress -> {
                                int downloadProgress = 40 + (int) (progress * 40 / tempDb.length());
                                callback.onProgress("正在下载: " + progress / 1024 + " KB", downloadProgress);
                            });

                            socket.close();

                            // 5. 验证下载的数据库
                            callback.onProgress("正在验证数据库完整性...", 85);

                            DatabaseSyncManager.getInstance().validateDatabase(tempDb)
                                .subscribe(isValid -> {
                                    if (!isValid) {
                                        emitter.onError(new Exception("下载的数据库文件无效"));
                                        return;
                                    }

                                    try {
                                        // 6. 关闭当前数据库连接
                                        SQLiteHelper.getInstance().close();

                                        // 7. 替换数据库文件
                                        callback.onProgress("正在替换数据库文件...", 90);

                                        File currentDb = context.getDatabasePath("HaoPray.db");
                                        boolean success = tempDb.renameTo(currentDb);

                                        if (success) {
                                            callback.onProgress("同步完成", 100);
                                            callback.onSuccess("数据库同步成功！文件大小: " +
                                                currentDb.length() / 1024 + " KB");
                                            emitter.onNext("同步成功");
                                        } else {
                                            emitter.onError(new Exception("无法替换数据库文件"));
                                        }

                                    } catch (Exception e) {
                                        emitter.onError(e);
                                    }

                                }, error -> emitter.onError(error));

                        } catch (Exception e) {
                            emitter.onError(e);
                        }
                    }, error -> emitter.onError(error));

            } catch (Exception e) {
                emitter.onError(e);
            }
        })
        .subscribeOn(Schedulers.io())
        .observeOn(AndroidSchedulers.mainThread())
        .doOnError(error -> {
            // 发生错误时的处理
            callback.onError("同步失败: " + error.getMessage(), error);

            // 尝试恢复最新备份
            if (backupFileRef[0] != null) {
                callback.onProgress("正在恢复备份数据库...", 0);
                DatabaseSyncManager.getInstance().restoreBackup(backupFileRef[0])
                    .subscribe(
                        success -> {
                            if (success) {
                                callback.onProgress("已恢复到同步前状态", 0);
                            }
                        },
                        restoreError -> {
                            // 恢复失败，记录错误但不再抛出异常
                            restoreError.printStackTrace();
                        }
                    );
            }
        })
        .doFinally(() -> {
            // 清理临时文件
            DatabaseSyncManager.getInstance().cleanupTempFiles();
        });
    }
    
    /**
     * 获取远程设备的数据库信息
     */
    public static Observable<JsonObject> getRemoteDatabaseInfo(DatabaseSyncManager.DeviceInfo device) {
        return Observable.<JsonObject>create(emitter -> {
            try {
                Socket socket = new Socket();
                socket.setSoTimeout(10000); // 10秒超时
                socket.connect(new java.net.InetSocketAddress(device.ip, device.port), 10000);
                
                OutputStream outputStream = socket.getOutputStream();
                OutputStreamProxy outputProxy = new OutputStreamProxy(outputStream);
                
                JsonObject request = new JsonObject();
                request.addProperty("action", "database_info");
                outputProxy.write(request.toString());
                socket.shutdownOutput();
                
                InputStream inputStream = socket.getInputStream();
                InputStreamProxy inputProxy = new InputStreamProxy(inputStream);
                String response = inputProxy.readString();
                
                socket.close();
                
                Gson gson = GsonUtils.getGson();
                JsonObject responseJson = gson.fromJson(response, JsonObject.class);
                
                emitter.onNext(responseJson);
                emitter.onComplete();
                
            } catch (Exception e) {
                emitter.onError(e);
            }
        })
        .subscribeOn(Schedulers.io())
        .observeOn(AndroidSchedulers.mainThread())
        .timeout(15, java.util.concurrent.TimeUnit.SECONDS);
    }
    
    /**
     * 检查服务器是否正在运行
     */
    public static boolean isServerRunning() {
        return serverRunning;
    }
}
