package com.haoxueren.helper;

import android.app.Activity;
import android.app.Application;
import android.os.Bundle;
import android.os.Handler;
import android.os.Looper;

import com.haoxueren.pray.log.MyLog;
import com.haoxueren.utils.Singleton;
import com.haoxueren.utils.ToastUtils;

import java.util.ArrayList;
import java.util.List;

public class LifecycleObserver implements Application.ActivityLifecycleCallbacks {

    private int foregroundActivityCount = 0;
    private final List<Activity> activityList = new ArrayList<>();
    private Activity currentActivity; // 当前活跃的Activity
    private final Handler handler = new Handler(Looper.getMainLooper());
    private final  Runnable task = () -> {
        for (Activity target : activityList) {
            ToastUtils.showToast("HaoPray已自动退出");
            target.finish();
        }
    };

    private LifecycleObserver() {
    }

    public static LifecycleObserver getInstance() {
        return new LifecycleObserver();
    }

    public void register(Application application) {
        application.registerActivityLifecycleCallbacks(this);
    }

    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
        activityList.add(activity);
    }

    @Override
    public void onActivityStarted(Activity activity) {
        foregroundActivityCount++;
        handler.removeCallbacks(task);
    }

    @Override
    public void onActivityResumed(Activity activity) {
        currentActivity = activity;
    }

    @Override
    public void onActivityPaused(Activity activity) {
        if (currentActivity == activity) {
            currentActivity = null;
        }
    }

    @Override
    public void onActivityStopped(Activity activity) {
        foregroundActivityCount--;
        if (foregroundActivityCount == 0) {
            handler.postDelayed(task, 60 * 1000L);
        }
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

    }

    @Override
    public void onActivityDestroyed(Activity activity) {
        activityList.remove(activity);
        if (currentActivity == activity) {
            currentActivity = null;
        }
    }

    /**
     * 获取当前活跃的Activity
     */
    public Activity getCurrentActivity() {
        return currentActivity;
    }

    /**
     * 检查应用是否在前台
     */
    public boolean isAppInForeground() {
        return foregroundActivityCount > 0;
    }

}
