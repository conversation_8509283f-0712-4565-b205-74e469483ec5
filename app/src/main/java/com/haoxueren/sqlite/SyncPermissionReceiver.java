package com.haoxueren.sqlite;

import android.content.BroadcastReceiver;
import android.content.Context;
import android.content.Intent;
import android.util.Log;

/**
 * 同步权限广播接收器
 * 处理通知中的用户操作（同意/拒绝）
 */
public class SyncPermissionReceiver extends BroadcastReceiver {
    
    private static final String TAG = "SyncPermissionReceiver";
    
    public static final String ACTION_ALLOW_SYNC = "ALLOW_SYNC";
    public static final String ACTION_DENY_SYNC = "DENY_SYNC";
    
    @Override
    public void onReceive(Context context, Intent intent) {
        String action = intent.getAction();
        String requestId = intent.getStringExtra("request_id");
        
        Log.i(TAG, "收到广播: action=" + action + ", requestId=" + requestId);
        
        if (requestId == null) {
            Log.w(TAG, "requestId为空，忽略广播");
            return;
        }
        
        boolean approved = false;
        
        switch (action) {
            case ACTION_ALLOW_SYNC:
                approved = true;
                Log.i(TAG, "用户通过通知同意同步请求: " + requestId);
                break;
                
            case ACTION_DENY_SYNC:
                approved = false;
                Log.i(TAG, "用户通过通知拒绝同步请求: " + requestId);
                break;
                
            default:
                Log.w(TAG, "未知的action: " + action);
                return;
        }
        
        // 处理用户响应
        SyncPermissionManager.getInstance().handleUserResponse(requestId, approved);
        
        // 取消通知
        SyncPermissionNotification notification = new SyncPermissionNotification(context);
        notification.cancelNotification();
    }
}
