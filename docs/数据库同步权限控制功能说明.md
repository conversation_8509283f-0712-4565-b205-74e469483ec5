# HaoPray 数据库同步权限控制功能说明

## 📋 功能概述

为了增强数据安全性，HaoPray应用新增了数据库同步权限控制功能。当其他设备请求同步本设备的数据库时，不再自动发送数据，而是需要用户明确确认后才能进行同步。

## ✨ 核心特性

### 🔒 权限控制机制
- **用户确认**：其他设备请求同步时，弹出确认对话框
- **超时保护**：30秒内无响应自动拒绝请求
- **通知支持**：应用在后台时通过通知提醒用户
- **安全提示**：清晰显示安全警告和设备信息

### 🎯 用户体验
- **直观界面**：清晰的同意/拒绝按钮
- **倒计时显示**：实时显示剩余确认时间
- **设备信息**：显示请求设备的详细信息
- **操作记录**：完整的日志记录所有操作

## 🏗️ 技术架构

### 核心组件

```
权限控制系统架构
├── SyncPermissionManager      # 权限管理器（核心控制逻辑）
├── SyncPermissionDialog       # 权限确认对话框
├── SyncPermissionNotification # 后台通知支持
├── SyncPermissionReceiver     # 通知操作广播接收器
├── SyncRequestInfo           # 同步请求信息封装
└── SyncPermissionTestUtils   # 测试工具类
```

### 工作流程

```mermaid
sequenceDiagram
    participant Client as 请求设备
    participant Server as 本设备服务器
    participant Manager as 权限管理器
    participant UI as 用户界面
    participant User as 用户

    Client->>Server: 发送同步请求
    Server->>Manager: 转发请求到权限管理器
    Manager->>UI: 显示权限确认对话框
    UI->>User: 询问是否同意同步
    
    alt 用户同意
        User->>UI: 点击"同意"
        UI->>Manager: 返回同意响应
        Manager->>Server: 发送数据库文件
        Server->>Client: 传输数据库
    else 用户拒绝
        User->>UI: 点击"拒绝"
        UI->>Manager: 返回拒绝响应
        Manager->>Server: 发送拒绝响应
        Server->>Client: 返回拒绝消息
    else 超时
        Note over Manager: 30秒超时
        Manager->>Server: 发送超时响应
        Server->>Client: 返回超时消息
    end
```

## 🚀 使用说明

### 正常使用流程

1. **启动应用**：确保HaoPray应用正常运行
2. **等待请求**：其他设备发起同步请求时会自动触发
3. **确认对话框**：
   - 查看请求设备信息
   - 阅读安全警告
   - 选择"同意"或"拒绝"
4. **完成同步**：同意后自动开始数据库传输

### 后台通知模式

当应用在后台时：
1. 系统显示权限确认通知
2. 点击通知可打开应用
3. 通知中直接点击"同意"或"拒绝"按钮
4. 自动处理用户选择

### 安全注意事项

⚠️ **重要提醒**：
- 只对信任的设备同意同步请求
- 同步会发送完整的数据库文件
- 包含所有祷告记录和分组信息
- 请确认网络环境安全

## 🔧 测试功能

### 测试入口
在数据库同步界面 → 菜单 → 测试功能 → 权限控制测试

### 测试项目
1. **基本权限控制流程测试**：测试完整的权限确认流程
2. **超时处理测试**：验证30秒超时机制
3. **用户同意响应测试**：模拟用户同意场景
4. **用户拒绝响应测试**：模拟用户拒绝场景
5. **清理过期请求测试**：测试请求清理机制
6. **运行所有权限测试**：执行完整测试套件

### 日志查看
所有测试结果和运行状态都会记录在系统日志中，可通过以下标签过滤：
- `SyncPermissionManager`：权限管理器日志
- `SyncPermissionTest`：测试工具日志
- `SyncPermissionDialog`：对话框日志

## 🛠️ 技术细节

### 超时机制
- **默认超时**：30秒
- **清理周期**：60秒清理一次过期请求
- **缓冲时间**：实际清理时给予2倍超时时间的缓冲

### 异步处理
- 使用`CompletableFuture`处理用户响应
- 主线程显示UI，后台线程处理网络通信
- 完整的异常处理和错误恢复机制

### 内存管理
- 自动清理过期请求避免内存泄漏
- 及时取消超时任务释放资源
- 正确关闭网络连接

## 📝 更新日志

### v1.0.0 (2024-07-23)
- ✅ 实现基本权限控制机制
- ✅ 添加用户确认对话框
- ✅ 支持后台通知确认
- ✅ 实现超时和错误处理
- ✅ 添加完整测试套件
- ✅ 集成到现有同步系统

## 🔮 未来计划

- [ ] 添加设备白名单功能
- [ ] 支持批量请求处理
- [ ] 增加更详细的设备指纹识别
- [ ] 实现请求历史记录
- [ ] 添加更多自定义超时选项

---

**注意**：此功能已完全集成到现有的数据库同步系统中，无需额外配置即可使用。所有现有的同步功能保持不变，只是增加了安全确认步骤。
