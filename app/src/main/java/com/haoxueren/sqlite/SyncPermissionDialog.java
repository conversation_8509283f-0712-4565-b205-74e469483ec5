package com.haoxueren.sqlite;

import android.app.Activity;
import android.app.AlertDialog;
import android.content.Context;
import android.os.Handler;
import android.os.Looper;
import android.view.LayoutInflater;
import android.view.View;
import android.widget.Button;
import android.widget.TextView;

import com.haoxueren.pray.R;

import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.Locale;
import java.util.function.Consumer;

/**
 * 同步权限确认对话框
 * 用于向用户确认是否允许其他设备同步本设备的数据库
 */
public class SyncPermissionDialog {
    
    private static final int DEFAULT_TIMEOUT_SECONDS = 30; // 默认30秒超时
    
    private Context context;
    private SyncRequestInfo requestInfo;
    private Consumer<Boolean> responseCallback;
    private AlertDialog dialog;
    private Handler handler;
    private Runnable timeoutRunnable;
    private int timeoutSeconds;
    private TextView countdownTextView;
    
    public SyncPermissionDialog(Context context, SyncRequestInfo requestInfo) {
        this.context = context;
        this.requestInfo = requestInfo;
        this.handler = new Handler(Looper.getMainLooper());
        this.timeoutSeconds = DEFAULT_TIMEOUT_SECONDS;
    }
    
    /**
     * 显示权限确认对话框
     * @param responseCallback 用户响应回调，true表示同意，false表示拒绝
     */
    public void show(Consumer<Boolean> responseCallback) {
        this.responseCallback = responseCallback;
        
        // 确保在主线程中显示对话框
        if (Looper.myLooper() != Looper.getMainLooper()) {
            handler.post(() -> showDialog());
        } else {
            showDialog();
        }
    }
    
    private void showDialog() {
        // 创建自定义布局
        LayoutInflater inflater = LayoutInflater.from(context);
        View dialogView = inflater.inflate(R.layout.dialog_sync_permission, null);
        
        // 初始化视图
        TextView titleTextView = dialogView.findViewById(R.id.titleTextView);
        TextView deviceInfoTextView = dialogView.findViewById(R.id.deviceInfoTextView);
        TextView messageTextView = dialogView.findViewById(R.id.messageTextView);
        countdownTextView = dialogView.findViewById(R.id.countdownTextView);
        Button allowButton = dialogView.findViewById(R.id.allowButton);
        Button denyButton = dialogView.findViewById(R.id.denyButton);
        
        // 设置内容
        titleTextView.setText("数据库同步请求");
        deviceInfoTextView.setText(requestInfo.getRequestDescription());
        
        SimpleDateFormat timeFormat = new SimpleDateFormat("HH:mm:ss", Locale.getDefault());
        String requestTimeStr = timeFormat.format(new Date(requestInfo.getRequestTime()));
        
        messageTextView.setText(String.format(
            "请求时间：%s\n\n" +
            "⚠️ 警告：同意此请求将会把您的完整数据库发送给请求设备。\n\n" +
            "请确认您信任该设备后再选择同意。如果您不确定，请选择拒绝。",
            requestTimeStr
        ));
        
        // 设置按钮点击事件
        allowButton.setOnClickListener(v -> {
            cancelTimeout();
            dismissDialog();
            if (responseCallback != null) {
                responseCallback.accept(true);
            }
        });
        
        denyButton.setOnClickListener(v -> {
            cancelTimeout();
            dismissDialog();
            if (responseCallback != null) {
                responseCallback.accept(false);
            }
        });
        
        // 创建对话框
        AlertDialog.Builder builder = new AlertDialog.Builder(context)
            .setView(dialogView)
            .setCancelable(false); // 不允许点击外部取消
        
        dialog = builder.create();
        
        // 显示对话框
        if (context instanceof Activity) {
            Activity activity = (Activity) context;
            if (!activity.isFinishing() && !activity.isDestroyed()) {
                dialog.show();
                startCountdown();
            }
        } else {
            dialog.show();
            startCountdown();
        }
    }
    
    /**
     * 开始倒计时
     */
    private void startCountdown() {
        updateCountdownText();
        
        timeoutRunnable = new Runnable() {
            @Override
            public void run() {
                timeoutSeconds--;
                if (timeoutSeconds > 0) {
                    updateCountdownText();
                    handler.postDelayed(this, 1000);
                } else {
                    // 超时处理
                    onTimeout();
                }
            }
        };
        
        handler.postDelayed(timeoutRunnable, 1000);
    }
    
    /**
     * 更新倒计时文本
     */
    private void updateCountdownText() {
        if (countdownTextView != null) {
            countdownTextView.setText(String.format("将在 %d 秒后自动拒绝", timeoutSeconds));
        }
    }
    
    /**
     * 取消超时
     */
    private void cancelTimeout() {
        if (timeoutRunnable != null) {
            handler.removeCallbacks(timeoutRunnable);
            timeoutRunnable = null;
        }
    }
    
    /**
     * 超时处理
     */
    private void onTimeout() {
        dismissDialog();
        if (responseCallback != null) {
            responseCallback.accept(false); // 超时默认拒绝
        }
    }
    
    /**
     * 关闭对话框
     */
    private void dismissDialog() {
        if (dialog != null && dialog.isShowing()) {
            dialog.dismiss();
            dialog = null;
        }
    }
    
    /**
     * 设置超时时间（秒）
     */
    public void setTimeoutSeconds(int timeoutSeconds) {
        this.timeoutSeconds = timeoutSeconds;
    }
    
    /**
     * 检查对话框是否正在显示
     */
    public boolean isShowing() {
        return dialog != null && dialog.isShowing();
    }
    
    /**
     * 强制关闭对话框
     */
    public void forceClose() {
        cancelTimeout();
        dismissDialog();
    }
}
