<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="24dp">

    <!-- 标题 -->
    <TextView
        android:id="@+id/titleTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="数据库同步请求"
        android:textSize="20sp"
        android:textStyle="bold"
        android:textColor="@android:color/black"
        android:gravity="center"
        android:layout_marginBottom="16dp" />

    <!-- 设备信息 -->
    <TextView
        android:id="@+id/deviceInfoTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="设备信息"
        android:textSize="16sp"
        android:textColor="@android:color/black"
        android:background="@drawable/rounded_background_light"
        android:padding="12dp"
        android:layout_marginBottom="16dp" />

    <!-- 详细消息 -->
    <TextView
        android:id="@+id/messageTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="详细消息"
        android:textSize="14sp"
        android:textColor="@android:color/black"
        android:lineSpacingExtra="4dp"
        android:layout_marginBottom="16dp" />

    <!-- 倒计时 -->
    <TextView
        android:id="@+id/countdownTextView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:text="将在 30 秒后自动拒绝"
        android:textSize="12sp"
        android:textColor="@android:color/holo_orange_dark"
        android:gravity="center"
        android:layout_marginBottom="20dp" />

    <!-- 按钮区域 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="center">

        <!-- 拒绝按钮 -->
        <Button
            android:id="@+id/denyButton"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="拒绝"
            android:textColor="@android:color/white"
            android:background="@drawable/button_deny_background"
            android:layout_marginEnd="8dp"
            android:textSize="16sp"
            android:textStyle="bold" />

        <!-- 同意按钮 -->
        <Button
            android:id="@+id/allowButton"
            android:layout_width="0dp"
            android:layout_height="48dp"
            android:layout_weight="1"
            android:text="同意"
            android:textColor="@android:color/white"
            android:background="@drawable/button_allow_background"
            android:layout_marginStart="8dp"
            android:textSize="16sp"
            android:textStyle="bold" />

    </LinearLayout>

</LinearLayout>
